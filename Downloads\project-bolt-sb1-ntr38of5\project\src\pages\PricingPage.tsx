import React from 'react';
import { motion } from 'framer-motion';
import { Check, Star, Zap, Crown } from 'lucide-react';

export function PricingPage() {
  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      description: 'Perfect for trying out our AI meeting intelligence',
      icon: <Star className="w-6 h-6" />,
      features: [
        '2 meeting transcriptions',
        'Basic AI summarization',
        'Action item extraction',
        'PDF export',
        'Email support'
      ],
      limitations: [
        'Limited to 2 meetings total',
        'Basic features only'
      ],
      buttonText: 'Get Started Free',
      buttonStyle: 'bg-gray-600 hover:bg-gray-700',
      popular: false
    },
    {
      name: 'Pro',
      price: '$19',
      period: 'per month',
      description: 'Ideal for professionals and small teams',
      icon: <Zap className="w-6 h-6" />,
      features: [
        '100 meeting transcriptions/month',
        'Advanced AI summarization',
        'Speaker identification',
        'Action item tracking',
        'Multiple export formats',
        'Search & filtering',
        'Priority support',
        'Team collaboration (up to 5 users)'
      ],
      buttonText: 'Start Pro Trial',
      buttonStyle: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',
      popular: true
    },
    {
      name: 'Enterprise',
      price: '$99',
      period: 'per month',
      description: 'For large teams and organizations',
      icon: <Crown className="w-6 h-6" />,
      features: [
        'Unlimited transcriptions',
        'Custom AI models',
        'Advanced analytics',
        'API access',
        'Custom integrations',
        'SSO & advanced security',
        'Dedicated support',
        'Unlimited team members',
        'Custom branding'
      ],
      buttonText: 'Contact Sales',
      buttonStyle: 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700',
      popular: false
    }
  ];

  const faqs = [
    {
      question: 'How accurate is the transcription?',
      answer: 'Our AI-powered transcription achieves 99%+ accuracy using OpenAI Whisper technology, even with multiple speakers and background noise.'
    },
    {
      question: 'What file formats do you support?',
      answer: 'We support all major audio and video formats including MP3, WAV, MP4, MOV, and M4A files up to 100MB in size.'
    },
    {
      question: 'Can I cancel my subscription anytime?',
      answer: 'Yes, you can cancel your subscription at any time. You\'ll continue to have access to your plan features until the end of your billing period.'
    },
    {
      question: 'Is my data secure?',
      answer: 'Absolutely. We use enterprise-grade encryption and security measures. Your meeting data is processed securely and never shared with third parties.'
    },
    {
      question: 'Do you offer team discounts?',
      answer: 'Yes, we offer custom pricing for teams of 10+ users. Contact our sales team to discuss volume discounts and enterprise features.'
    }
  ];

  return (
    <div className="py-12">
      {/* Header */}
      <section className="container mx-auto px-4 text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-800 dark:text-white mb-4">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Transform your meetings with AI-powered transcription and summarization. 
            Start free, upgrade when you need more.
          </p>
        </motion.div>
      </section>

      {/* Pricing Cards */}
      <section className="container mx-auto px-4 mb-20">
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`relative bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/20 p-8 ${
                plan.popular ? 'ring-2 ring-blue-500 scale-105' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <div className={`inline-flex p-3 rounded-xl mb-4 ${
                  plan.popular 
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                }`}>
                  {plan.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                  {plan.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {plan.description}
                </p>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-800 dark:text-white">
                    {plan.price}
                  </span>
                  <span className="text-gray-600 dark:text-gray-300 ml-2">
                    {plan.period}
                  </span>
                </div>
              </div>

              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>

              {plan.limitations && (
                <div className="mb-6 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200 font-medium mb-2">
                    Limitations:
                  </p>
                  <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                    {plan.limitations.map((limitation, limitIndex) => (
                      <li key={limitIndex}>• {limitation}</li>
                    ))}
                  </ul>
                </div>
              )}

              <button
                className={`w-full py-3 px-6 rounded-xl font-semibold text-white transition-all transform hover:scale-105 ${plan.buttonStyle}`}
              >
                {plan.buttonText}
              </button>
            </motion.div>
          ))}
        </div>
      </section>

      {/* Features Comparison */}
      <section className="container mx-auto px-4 mb-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
            Compare Features
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            See what's included in each plan
          </p>
        </motion.div>

        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/20 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700/50">
                <tr>
                  <th className="text-left p-6 font-semibold text-gray-800 dark:text-white">
                    Features
                  </th>
                  <th className="text-center p-6 font-semibold text-gray-800 dark:text-white">
                    Free
                  </th>
                  <th className="text-center p-6 font-semibold text-gray-800 dark:text-white">
                    Pro
                  </th>
                  <th className="text-center p-6 font-semibold text-gray-800 dark:text-white">
                    Enterprise
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {[
                  ['Monthly Transcriptions', '2 total', '100', 'Unlimited'],
                  ['AI Summarization', '✓', '✓', '✓'],
                  ['Speaker Identification', '✗', '✓', '✓'],
                  ['Action Item Extraction', '✓', '✓', '✓'],
                  ['Export Formats', 'PDF', 'PDF, Word, JSON', 'All formats'],
                  ['Team Collaboration', '✗', 'Up to 5 users', 'Unlimited'],
                  ['API Access', '✗', '✗', '✓'],
                  ['Priority Support', '✗', '✓', '✓'],
                  ['Custom Integrations', '✗', '✗', '✓']
                ].map((row, index) => (
                  <tr key={index}>
                    <td className="p-6 font-medium text-gray-800 dark:text-white">
                      {row[0]}
                    </td>
                    <td className="p-6 text-center text-gray-600 dark:text-gray-300">
                      {row[1]}
                    </td>
                    <td className="p-6 text-center text-gray-600 dark:text-gray-300">
                      {row[2]}
                    </td>
                    <td className="p-6 text-center text-gray-600 dark:text-gray-300">
                      {row[3]}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Got questions? We've got answers.
          </p>
        </motion.div>

        <div className="max-w-3xl mx-auto space-y-6">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/20 p-6"
            >
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
                {faq.question}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                {faq.answer}
              </p>
            </motion.div>
          ))}
        </div>
      </section>
    </div>
  );
}
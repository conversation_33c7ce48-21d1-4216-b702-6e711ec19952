import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Plus, 
  FileText, 
  Clock, 
  TrendingUp, 
  Users,
  Calendar,
  BarChart3,
  Upload,
  Mic
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { format } from 'date-fns';

interface Meeting {
  id: string;
  title: string;
  duration: string | null;
  status: 'processing' | 'completed' | 'failed';
  created_at: string;
}

export function Dashboard() {
  const { user, userProfile } = useAuth();
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalMeetings: 0,
    totalDuration: 0,
    thisWeek: 0,
    avgDuration: 0
  });

  useEffect(() => {
    fetchMeetings();
  }, [user]);

  const fetchMeetings = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('meetings')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      setMeetings(data || []);
      
      // Calculate stats
      const totalMeetings = data?.length || 0;
      const completedMeetings = data?.filter(m => m.status === 'completed') || [];
      const totalMinutes = completedMeetings.reduce((acc, meeting) => {
        if (meeting.duration) {
          const [hours, minutes, seconds] = meeting.duration.split(':').map(Number);
          return acc + (hours * 60) + minutes + (seconds / 60);
        }
        return acc;
      }, 0);

      const thisWeekMeetings = data?.filter(meeting => {
        const meetingDate = new Date(meeting.created_at);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return meetingDate >= weekAgo;
      }).length || 0;

      setStats({
        totalMeetings,
        totalDuration: Math.round(totalMinutes),
        thisWeek: thisWeekMeetings,
        avgDuration: completedMeetings.length > 0 ? Math.round(totalMinutes / completedMeetings.length) : 0
      });
    } catch (error) {
      console.error('Error fetching meetings:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const canCreateMeeting = userProfile && userProfile.usage_count < userProfile.usage_limit;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
          Welcome back, {userProfile?.full_name || user?.email?.split('@')[0] || 'User'}!
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Here's what's happening with your meetings today.
        </p>
      </motion.div>

      {/* Usage Status */}
      {userProfile && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                Usage Status
              </h3>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                userProfile.subscription_tier === 'free' 
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
              }`}>
                {userProfile.subscription_tier.charAt(0).toUpperCase() + userProfile.subscription_tier.slice(1)} Plan
              </span>
            </div>
            
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-1">
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
                  <span>Transcriptions Used</span>
                  <span>{userProfile.usage_count}/{userProfile.usage_limit}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min((userProfile.usage_count / userProfile.usage_limit) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {!canCreateMeeting && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                  You've reached your usage limit. 
                  <Link to="/pricing" className="font-medium underline ml-1">
                    Upgrade your plan
                  </Link> to continue transcribing meetings.
                </p>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="mb-8"
      >
        <div className="grid sm:grid-cols-2 gap-4">
          <Link
            to={canCreateMeeting ? "/upload" : "/pricing"}
            className={`bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-2xl p-6 shadow-xl transition-all transform hover:scale-105 ${
              !canCreateMeeting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <Upload className="w-6 h-6" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-1">Upload Meeting</h3>
                <p className="text-blue-100">Upload audio or video files</p>
              </div>
            </div>
          </Link>

          <Link
            to={canCreateMeeting ? "/record" : "/pricing"}
            className={`bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-2xl p-6 shadow-xl transition-all transform hover:scale-105 ${
              !canCreateMeeting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <Mic className="w-6 h-6" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-1">Record Live</h3>
                <p className="text-purple-100">Record directly in browser</p>
              </div>
            </div>
          </Link>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
      >
        {[
          {
            icon: <FileText className="w-6 h-6" />,
            label: "Total Meetings",
            value: stats.totalMeetings,
            color: "text-blue-600 dark:text-blue-400"
          },
          {
            icon: <Clock className="w-6 h-6" />,
            label: "Total Duration",
            value: `${Math.floor(stats.totalDuration / 60)}h ${stats.totalDuration % 60}m`,
            color: "text-green-600 dark:text-green-400"
          },
          {
            icon: <TrendingUp className="w-6 h-6" />,
            label: "This Week",
            value: stats.thisWeek,
            color: "text-purple-600 dark:text-purple-400"
          },
          {
            icon: <BarChart3 className="w-6 h-6" />,
            label: "Avg Duration",
            value: `${stats.avgDuration}m`,
            color: "text-orange-600 dark:text-orange-400"
          }
        ].map((stat, index) => (
          <div
            key={index}
            className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className={stat.color}>
                {stat.icon}
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {stat.label}
              </span>
            </div>
            <p className="text-2xl font-bold text-gray-800 dark:text-white">
              {stat.value}
            </p>
          </div>
        ))}
      </motion.div>

      {/* Recent Meetings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/20"
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
              Recent Meetings
            </h2>
            <Link
              to="/meetings"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
            >
              View All
            </Link>
          </div>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          ) : meetings.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                No meetings yet
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                Upload your first meeting recording to get started
              </p>
              <Link
                to={canCreateMeeting ? "/upload" : "/pricing"}
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
              >
                <Plus className="w-4 h-4" />
                Create Meeting
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {meetings.map((meeting) => (
                <div
                  key={meeting.id}
                  className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-800 dark:text-white">
                        {meeting.title}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {format(new Date(meeting.created_at), 'MMM d, yyyy')}
                        </span>
                        {meeting.duration && (
                          <span className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {meeting.duration}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(meeting.status)}`}>
                      {meeting.status.charAt(0).toUpperCase() + meeting.status.slice(1)}
                    </span>
                    {meeting.status === 'completed' && (
                      <Link
                        to={`/meeting/${meeting.id}`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm"
                      >
                        View
                      </Link>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}
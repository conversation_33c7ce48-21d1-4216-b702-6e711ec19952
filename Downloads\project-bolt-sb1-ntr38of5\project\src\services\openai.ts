// OpenAI API integration service
export class OpenAIService {
  private apiKey: string;
  private baseUrl = 'https://api.openai.com/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async transcribeAudio(audioFile: File): Promise<{
    text: string;
    segments: Array<{
      text: string;
      start: number;
      end: number;
      speaker?: string;
    }>;
  }> {
    const formData = new FormData();
    formData.append('file', audioFile);
    formData.append('model', 'whisper-1');
    formData.append('response_format', 'verbose_json');
    formData.append('timestamp_granularities[]', 'segment');

    try {
      const response = await fetch(`${this.baseUrl}/audio/transcriptions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Process segments and add basic speaker diarization
      const segments = data.segments?.map((segment: any, index: number) => ({
        text: segment.text,
        start: segment.start,
        end: segment.end,
        speaker: `Speaker ${(index % 2) + 1}`, // Simple alternating speaker assignment
      })) || [];

      return {
        text: data.text,
        segments,
      };
    } catch (error) {
      console.error('Transcription error:', error);
      throw error;
    }
  }

  async generateSummary(transcript: string): Promise<{
    summary: string;
    keyPoints: string[];
    actionItems: Array<{
      task: string;
      assignee?: string;
      priority: 'high' | 'medium' | 'low';
      dueDate?: string;
    }>;
  }> {
    const prompt = `
Please analyze the following meeting transcript and provide:
1. A concise summary of the meeting
2. Key points discussed
3. Action items with priorities

Transcript:
${transcript}

Please format your response as JSON with the following structure:
{
  "summary": "Brief meeting summary",
  "keyPoints": ["Point 1", "Point 2", ...],
  "actionItems": [
    {
      "task": "Task description",
      "assignee": "Person responsible (if mentioned)",
      "priority": "high|medium|low",
      "dueDate": "YYYY-MM-DD (if mentioned)"
    }
  ]
}
`;

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are an AI assistant specialized in analyzing meeting transcripts and extracting key information.',
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: 0.3,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.choices[0].message.content;
      
      try {
        return JSON.parse(content);
      } catch (parseError) {
        // Fallback if JSON parsing fails
        return {
          summary: content,
          keyPoints: [],
          actionItems: [],
        };
      }
    } catch (error) {
      console.error('Summary generation error:', error);
      throw error;
    }
  }
}

// Mock service for development/demo purposes
export class MockOpenAIService {
  async transcribeAudio(audioFile: File): Promise<{
    text: string;
    segments: Array<{
      text: string;
      start: number;
      end: number;
      speaker?: string;
    }>;
  }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const mockTranscript = `Good morning everyone, thanks for joining our quarterly planning meeting. Let's start by reviewing our Q4 objectives and discussing the roadmap for next quarter. As we can see from the metrics, our user engagement has increased by 23% compared to last quarter, which is excellent news for the product team. That's fantastic progress! I'd like to add that our conversion rates have also improved significantly. We should definitely prioritize the mobile app improvements for next quarter. I agree with the mobile focus. Our analytics show that 67% of our users are accessing the platform via mobile devices, so this should be our top priority.`;

    const segments = [
      {
        text: "Good morning everyone, thanks for joining our quarterly planning meeting. Let's start by reviewing our Q4 objectives and discussing the roadmap for next quarter.",
        start: 0,
        end: 8,
        speaker: "Speaker 1"
      },
      {
        text: "As we can see from the metrics, our user engagement has increased by 23% compared to last quarter, which is excellent news for the product team.",
        start: 8,
        end: 16,
        speaker: "Speaker 1"
      },
      {
        text: "That's fantastic progress! I'd like to add that our conversion rates have also improved significantly. We should definitely prioritize the mobile app improvements for next quarter.",
        start: 16,
        end: 25,
        speaker: "Speaker 2"
      },
      {
        text: "I agree with the mobile focus. Our analytics show that 67% of our users are accessing the platform via mobile devices, so this should be our top priority.",
        start: 25,
        end: 33,
        speaker: "Speaker 1"
      }
    ];

    return {
      text: mockTranscript,
      segments
    };
  }

  async generateSummary(transcript: string): Promise<{
    summary: string;
    keyPoints: string[];
    actionItems: Array<{
      task: string;
      assignee?: string;
      priority: 'high' | 'medium' | 'low';
      dueDate?: string;
    }>;
  }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    return {
      summary: "This quarterly planning meeting focused on reviewing Q4 performance metrics and establishing priorities for the upcoming quarter. The team discussed significant improvements in user engagement and conversion rates, with particular emphasis on mobile platform optimization.",
      keyPoints: [
        "User engagement increased by 23% compared to previous quarter",
        "Conversion rates showed significant improvement",
        "67% of users access the platform via mobile devices",
        "Mobile app improvements identified as top priority for next quarter",
        "Team alignment on focusing resources on mobile optimization"
      ],
      actionItems: [
        {
          task: "Finalize mobile app improvement roadmap",
          assignee: "Product Team",
          priority: "high",
          dueDate: "2024-02-15"
        },
        {
          task: "Conduct detailed mobile user experience audit",
          assignee: "UX Team",
          priority: "medium",
          dueDate: "2024-02-10"
        },
        {
          task: "Prepare Q4 performance presentation for stakeholders",
          assignee: "Analytics Team",
          priority: "medium",
          dueDate: "2024-02-20"
        }
      ]
    };
  }
}
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Upload, FileAudio, FileVideo, X, AlertCircle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { OpenAIService } from '../services/openai';
import toast from 'react-hot-toast';

export function UploadPage() {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const { user, userProfile, refreshUserProfile } = useAuth();
  const navigate = useNavigate();

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (isValidFileType(file)) {
        setSelectedFile(file);
      } else {
        toast.error('Please select a valid audio or video file (MP3, WAV, MP4, M4A)');
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (isValidFileType(file)) {
        setSelectedFile(file);
      } else {
        toast.error('Please select a valid audio or video file (MP3, WAV, MP4, M4A)');
      }
    }
  };

  const isValidFileType = (file: File) => {
    const validTypes = [
      'audio/mp3', 
      'audio/mpeg', 
      'audio/wav', 
      'audio/x-wav',
      'audio/mp4',
      'audio/m4a',
      'video/mp4', 
      'video/quicktime'
    ];
    return validTypes.includes(file.type) || file.name.match(/\.(mp3|wav|mp4|mov|m4a)$/i);
  };

  const handleUpload = async () => {
    if (!selectedFile || !user) return;

    // Check usage limits
    if (userProfile && userProfile.usage_count >= userProfile.usage_limit) {
      toast.error('You have reached your usage limit. Please upgrade your plan.');
      navigate('/pricing');
      return;
    }

    setUploading(true);
    setProcessing(true);

    try {
      // Create meeting record
      const { data: meeting, error: meetingError } = await supabase
        .from('meetings')
        .insert({
          user_id: user.id,
          title: selectedFile.name.replace(/\.[^/.]+$/, ''),
          file_name: selectedFile.name,
          status: 'processing'
        })
        .select()
        .single();

      if (meetingError) throw meetingError;

      // Simulate file upload and processing
      const openaiService = new MockOpenAIService();
      
      // Transcribe audio
      const transcriptionResult = await openaiService.transcribeAudio(selectedFile);
      
      // Generate summary
      const summaryResult = await openaiService.generateSummary(transcriptionResult.text);

      // Calculate duration (mock)
      const duration = '00:15:30'; // Mock duration

      // Update meeting with results
      const { error: updateError } = await supabase
        .from('meetings')
        .update({
          duration,
          transcript: {
            text: transcriptionResult.text,
            segments: transcriptionResult.segments
          },
          summary: {
            overview: summaryResult.summary,
            keyPoints: summaryResult.keyPoints
          },
          action_items: summaryResult.actionItems,
          status: 'completed'
        })
        .eq('id', meeting.id);

      if (updateError) throw updateError;

      // Update user usage count
      if (userProfile) {
        const { error: usageError } = await supabase
          .from('users')
          .update({ usage_count: userProfile.usage_count + 1 })
          .eq('id', user.id);

        if (usageError) throw usageError;
        await refreshUserProfile();
      }

      toast.success('Meeting processed successfully!');
      navigate(`/meeting/${meeting.id}`);

    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error('Failed to process meeting. Please try again.');
    } finally {
      setUploading(false);
      setProcessing(false);
    }
  };

  const clearFile = () => {
    setSelectedFile(null);
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('video/') || file.name.match(/\.(mp4|mov)$/i)) {
      return <FileVideo className="w-8 h-8 text-purple-500" />;
    }
    return <FileAudio className="w-8 h-8 text-blue-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Check if user can upload
  const canUpload = userProfile && userProfile.usage_count < userProfile.usage_limit;

  if (!canUpload) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl p-8 shadow-xl border border-white/20 dark:border-gray-700/20 text-center"
          >
            <AlertCircle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
              Usage Limit Reached
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              You've used all {userProfile?.usage_limit || 2} of your free transcriptions. 
              Upgrade your plan to continue processing meetings.
            </p>
            <button
              onClick={() => navigate('/pricing')}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-semibold transition-all transform hover:scale-105"
            >
              Upgrade Plan
            </button>
          </motion.div>
        </div>
      </div>
    );
  }

  if (processing) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl p-8 shadow-xl border border-white/20 dark:border-gray-700/20 text-center"
          >
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
              <Upload className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
              Processing Your Meeting
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Our AI is transcribing and analyzing your meeting. This usually takes 30-60 seconds.
            </p>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
              <div className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full animate-pulse" style={{ width: '70%' }}></div>
            </div>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                <span>Transcribing audio with AI</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <span>Generating summary and insights</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <span>Extracting action items</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
            Upload Meeting Recording
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Upload your audio or video files and let AI transform them into actionable insights
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-2xl p-8 shadow-xl border border-white/20 dark:border-gray-700/20"
        >
          {!selectedFile ? (
            <div
              className={`border-2 border-dashed rounded-xl p-12 transition-all text-center ${
                dragActive
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                type="file"
                id="file-upload"
                className="hidden"
                accept=".mp3,.wav,.mp4,.mov,.m4a"
                onChange={handleChange}
              />
              <label htmlFor="file-upload" className="cursor-pointer">
                <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                  Drop files here or click to browse
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Support for MP3, WAV, MP4, MOV, and M4A files
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Maximum file size: 100MB
                </p>
              </label>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {getFileIcon(selectedFile)}
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white">
                        {selectedFile.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {formatFileSize(selectedFile.size)}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={clearFile}
                    className="p-2 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full transition-colors"
                  >
                    <X className="w-5 h-5 text-gray-500" />
                  </button>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                  What happens next?
                </h4>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• AI will transcribe your audio with 99%+ accuracy</li>
                  <li>• Speakers will be automatically identified</li>
                  <li>• Key points and action items will be extracted</li>
                  <li>• You'll get a complete summary in minutes</li>
                </ul>
              </div>

              <button
                onClick={handleUpload}
                disabled={uploading}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-xl transition-all transform hover:scale-105 flex items-center justify-center gap-2"
              >
                {uploading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="w-5 h-5" />
                    Process Meeting
                  </>
                )}
              </button>
            </div>
          )}
        </motion.div>

        {/* Usage indicator */}
        {userProfile && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-6 text-center"
          >
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {userProfile.usage_limit - userProfile.usage_count} transcription{userProfile.usage_limit - userProfile.usage_count !== 1 ? 's' : ''} remaining
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
}